/**
 * CliniCore Webhook Handler
 *
 * Handles incoming webhook events from CliniCore platform.
 * Processes patient creation and update events with proper validation,
 * error handling, and database logging.
 */

import type { CCPatientWebhookPayload, CCWebhookPayload } from "@type";
import type { Context } from "hono";
import {
	patientCreateProcessor,
	validatePatientWebhookPayload,
} from "@/processors/patientProcessor";
import { logValidationError, logWebhookError } from "@/utils/errorLogger";

/**
 * Handle CliniCore webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function handleCCWebhook(c: Context): Promise<Response> {
	const startTime = Date.now();
	const requestId = c.get("requestId");

	try {
		console.log(`[${requestId}] CC webhook received`);

		// Parse JSON payload
		let payload: unknown;
		try {
			payload = await c.req.json();
		} catch (error) {
			await logValidationError(error as Error, requestId, "json_parsing");
			return c.json(
				{
					error: "Invalid JSON payload",
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Validate webhook payload structure
		let validatedPayload: CCWebhookPayload;
		try {
			validatedPayload = payload as CCWebhookPayload;

			// Basic validation
			if (
				!validatedPayload.event ||
				!validatedPayload.model ||
				!validatedPayload.payload
			) {
				throw new Error("Missing required fields: event, model, or payload");
			}
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"webhook_structure",
				payload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid payload structure: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Only process patient creation events for now
		if (
			validatedPayload.model !== "Patient" ||
			validatedPayload.event !== "EntityWasCreated"
		) {
			console.log(
				`[${requestId}] Ignoring event: ${validatedPayload.event} for model: ${validatedPayload.model}`,
			);
			return c.json(
				{
					message: "Event ignored - only processing patient creation events",
					event: validatedPayload.event,
					model: validatedPayload.model,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		}

		// Validate patient-specific payload
		let patientPayload: CCPatientWebhookPayload;
		try {
			patientPayload = validatePatientWebhookPayload(validatedPayload);
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"patient_payload",
				validatedPayload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid patient payload: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Process patient creation
		try {
			await patientCreateProcessor(patientPayload);

			const processingTime = Date.now() - startTime;
			console.log(
				`[${requestId}] Patient processing completed in ${processingTime}ms`,
			);

			return c.json(
				{
					message: "Patient processed successfully",
					patientId: patientPayload.payload.id,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		} catch (error) {
			const processingTime = Date.now() - startTime;
			await logWebhookError(error as Error, requestId, {
				event: patientPayload.event,
				model: patientPayload.model,
				patientId: patientPayload.payload.id,
				processingTime,
			});

			return c.json(
				{
					error: "Patient processing failed",
					details: String(error),
					patientId: patientPayload.payload.id,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				500,
			);
		}
	} catch (error) {
		const processingTime = Date.now() - startTime;
		await logWebhookError(error as Error, requestId, {
			processingTime,
			stage: "general_processing",
		});

		return c.json(
			{
				error: "Internal server error",
				details: String(error),
				processingTime,
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
