/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out invoice/payment related fields (excluded fields)
 * 3. Mapping CC custom field values to AP custom field format
 * 4. Creating AP custom fields if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Exclusions: All invoice-related and payment-related custom fields are skipped
 * to prevent conflicts with dedicated invoice/payment processing workflows.
 */

import type {
	APGetCustomFieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";

/**
 * Invoice-related custom field names to exclude from sync
 * These are managed by dedicated invoice processing workflows
 */
const INVOICE_RELATED_FIELDS = [
	"Latest Invoice PDF URL",
	"Latest Gross Amount",
	"Latest Discount",
	"Latest Total Amount",
	"Latest Products",
	"Latest Diagnosis",
	"Latest Treated By",
	"Gross Amount",
	"Discount",
	"Total Amount",
	"Products",
	"The Diagnosis",
	"Treated By",
	"Extra Discount",
];

/**
 * Payment-related custom field names to exclude from sync
 * These are managed by dedicated payment processing workflows
 */
const PAYMENT_RELATED_FIELDS = [
	"Latest Payment Status",
	"Latest Amount Paid",
	"Latest Payment Date",
	"Latest Payment PDF URL",
	"Due amount",
	"Credit amount",
	"Total Invoiced Amount",
	"Life Time Value",
	"LTV",
];

/**
 * All excluded field names (invoice + payment related)
 */
const EXCLUDED_FIELD_NAMES = [
	...INVOICE_RELATED_FIELDS,
	...PAYMENT_RELATED_FIELDS,
];

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	const requestId = `cf-sync-${localPatientId}`;

	console.log(
		`[${requestId}] Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId}`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		console.log(
			`[${requestId}] No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		console.log(
			`[${requestId}] Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			console.log(`[${requestId}] No custom field data returned from CC`);
			return;
		}

		// Step 3: Filter out excluded fields and extract valid field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			console.log(
				`[${requestId}] No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping
		console.log(`[${requestId}] Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			console.log(`[${requestId}] No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		console.log(
			`[${requestId}] Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		await contactReq.update(apContactId, {
			customFields: apCustomFieldMappings,
		});

		console.log(`[${requestId}] Custom field sync completed successfully`);
	} catch (error) {
		console.error(`[${requestId}] Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes invoice/payment related fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field should be excluded
		if (
			EXCLUDED_FIELD_NAMES.includes(fieldName) ||
			EXCLUDED_FIELD_NAMES.includes(fieldLabel)
		) {
			console.log(
				`[${requestId}] Excluding field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Extract field value
		if (!ccCustomField.values || ccCustomField.values.length === 0) {
			console.log(`[${requestId}] Skipping field with no values: ${fieldName}`);
			continue;
		}

		// Combine multiple values with comma separation
		const fieldValue = ccCustomField.values
			.map((v) => v.value)
			.filter((v) => v && v.trim() !== "")
			.join(", ");

		if (!fieldValue) {
			console.log(
				`[${requestId}] Skipping field with empty value: ${fieldName}`,
			);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		console.log(`[${requestId}] Valid field: ${fieldName} = "${fieldValue}"`);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field by name or label
			let apCustomField = apCustomFields.find(
				(apField) =>
					apField.name === field.name || apField.name === field.label,
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				console.log(
					`[${requestId}] Creating new AP custom field: ${field.label}`,
				);
				apCustomField = await apCustomfield.create({
					name: field.label, // Use label as it's more user-friendly
					dataType: "TEXT",
				});

				// Add to our local cache to avoid duplicate creation
				apCustomFields.push(apCustomField);
			}

			mappings.push({
				id: apCustomField.id,
				value: value,
			});

			console.log(
				`[${requestId}] Mapped: ${field.label} -> AP Field ID ${apCustomField.id}`,
			);
		} catch (error) {
			console.error(`[${requestId}] Failed to map field ${field.name}:`, error);
			// Continue with other fields rather than failing completely
		}
	}

	return mappings;
}
