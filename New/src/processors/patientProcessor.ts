/**
 * Patient Processor for CC Webhook Events
 *
 * Handles patient creation events from ClinicalCare webhooks by:
 * 1. Validating webhook payload
 * 2. Looking up existing patient in local database
 * 3. Syncing with AutoPatient platform
 * 4. Updating local database with results
 *
 * Performance Critical: Must complete within 20 seconds (Cloudflare Workers timeout)
 */

import { dbSchema, getDb } from "@database";
import type {
	CCPatientWebhookPayload,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { eq, or } from "drizzle-orm";
import { contactReq } from "@/apiClient";
import { syncCcToApCustomFields } from "./ccToApCustomFieldsProcessor";

/**
 * Process patient creation webhook from ClinicalCare
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param payload - Validated CC patient webhook payload
 * @returns Promise<void> - Completes processing or throws error
 */
export async function patientCreateProcessor(
	requestId: string,
	payload: CCPatientWebhookPayload,
): Promise<void> {
	const ccPatientData = payload.payload;
	const ccId = ccPatientData.id;
	const email = ccPatientData.email?.trim() || null;
	const phone = ccPatientData.phoneMobile?.trim() || null;

	console.log(
		`[${requestId}] Processing patient creation for CC ID: ${ccId}, Email: ${email}, Phone: ${phone}`,
	);

	// Step 1: Look up existing patient in local database
	const db = getDb();
	let existingPatient: typeof dbSchema.patient.$inferSelect | undefined;

	try {
		// Primary lookup by ccId
		const ccIdResults = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.ccId, ccId))
			.limit(1);

		if (ccIdResults.length > 0) {
			existingPatient = ccIdResults[0];
			console.log(`Found existing patient by CC ID: ${existingPatient.id}`);
		} else {
			// Secondary lookup by email or phone
			const conditions = [];
			if (email) {
				conditions.push(eq(dbSchema.patient.email, email));
			}
			if (phone) {
				conditions.push(eq(dbSchema.patient.phone, phone));
			}

			if (conditions.length > 0) {
				const contactResults = await db
					.select()
					.from(dbSchema.patient)
					.where(or(...conditions))
					.limit(1);

				if (contactResults.length > 0) {
					existingPatient = contactResults[0];
					console.log(
						`Found existing patient by email/phone: ${existingPatient.id}`,
					);
				}
			}
		}
	} catch (error) {
		console.error("Database lookup error:", error);
		throw new Error(`Failed to lookup existing patient: ${error}`);
	}

	// Step 2: Update or create local patient record
	const now = new Date();
	let localPatientId: string;

	try {
		if (existingPatient) {
			// Update existing patient with new CC data
			await db
				.update(dbSchema.patient)
				.set({
					ccId: ccId,
					email: email,
					phone: phone,
					ccData: ccPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, existingPatient.id));

			localPatientId = existingPatient.id;
			console.log(`Updated existing patient: ${localPatientId}`);
		} else {
			// Create new patient record
			const newPatientResults = await db
				.insert(dbSchema.patient)
				.values({
					ccId: ccId,
					email: email,
					phone: phone,
					ccData: ccPatientData,
					ccUpdatedAt: now,
				})
				.returning({ id: dbSchema.patient.id });

			localPatientId = newPatientResults[0].id;
			console.log(`Created new patient: ${localPatientId}`);
		}
	} catch (error) {
		console.error("Database update error:", error);
		throw new Error(`Failed to update local patient record: ${error}`);
	}

	// Step 3: Sync with AutoPatient
	let finalApId: string | null = null;
	try {
		finalApId = await syncWithAutoPatient(localPatientId, ccPatientData, email, phone);
	} catch (error) {
		console.error("AutoPatient sync error:", error);
		// Don't throw here - we want to complete the webhook even if AP sync fails
		// The error will be logged and can be retried later
	}

	// Step 4: Sync custom fields from CC to AP (non-blocking, only if AP sync succeeded)
	if (finalApId) {
		try {
			console.log(`[${requestId}] Starting custom field synchronization...`);
			await syncCcToApCustomFields(requestId, localPatientId, ccPatientData, finalApId);
			console.log(`[${requestId}] Custom field synchronization completed`);
		} catch (error) {
			console.error(`[${requestId}] Custom field sync error:`, error);
			// Don't throw here - custom field sync failure shouldn't block main patient processing
			// The error is already logged in the custom field processor
		}
	} else {
		console.log(`[${requestId}] Skipping custom field sync - AP sync failed`);
	}

	console.log(`[${requestId}] Patient processing completed for CC ID: ${ccId}`);
}

/**
 * Sync patient data with AutoPatient platform
 *
 * @param localPatientId - Local database patient ID
 * @param ccPatientData - CC patient data
 * @param email - Patient email
 * @param phone - Patient phone
 * @returns Promise<string> - AP contact ID
 */
async function syncWithAutoPatient(
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	email: string | null,
	phone: string | null,
): Promise<string> {
	const db = getDb();
	const now = new Date();

	// Convert CC patient data to AP contact format for upsert
	// Include gender field since upsert can handle it for both create and update
	const apContactData: PostAPContactType = {
		firstName: ccPatientData.firstName || null,
		lastName: ccPatientData.lastName || null,
		email: email,
		phone: phone,
		dateOfBirth: ccPatientData.dob || null,
		gender: ccPatientData.gender || null,
		source: "cc",
		// Add address if available
		...(ccPatientData.addresses &&
			ccPatientData.addresses.length > 0 && {
				address1: ccPatientData.addresses[0].street || null,
				city: ccPatientData.addresses[0].city || null,
				state: ccPatientData.addresses[0].state || null,
				postalCode: ccPatientData.addresses[0].zip || null,
			}),
		// Exclude tags from upsert to prevent removing existing tags
	};

	// Step 1: Upsert contact with gender field (works for both create and update)
	console.log("Upserting AP contact with gender field...");
	const apContactResult = await contactReq.upsert(apContactData);
	const finalApId = apContactResult.id;

	// Update local database with AP sync results
	await db
		.update(dbSchema.patient)
		.set({
			apId: finalApId,
			apData: apContactResult,
			apUpdatedAt: now,
			updatedAt: now,
		})
		.where(eq(dbSchema.patient.id, localPatientId));

	console.log(`AP sync completed. AP ID: ${finalApId}`);
	return finalApId;
}

/**
 * Validate CC patient webhook payload
 *
 * @param payload - Raw webhook payload
 * @returns Validated payload or throws error
 */
export function validatePatientWebhookPayload(
	payload: unknown,
): CCPatientWebhookPayload {
	if (!payload || typeof payload !== "object") {
		throw new Error("Invalid payload: must be an object");
	}

	const p = payload as Record<string, unknown>;

	if (p.event !== "EntityWasCreated" && p.event !== "EntityWasUpdated") {
		throw new Error(
			`Invalid event type: ${p.event}. Expected EntityWasCreated or EntityWasUpdated`,
		);
	}

	if (p.model !== "Patient") {
		throw new Error(`Invalid model: ${p.model}. Expected Patient`);
	}

	if (typeof p.id !== "number") {
		throw new Error("Invalid id: must be a number");
	}

	if (!p.payload || typeof p.payload !== "object") {
		throw new Error("Invalid payload.payload: must be an object");
	}

	if (typeof p.timestamp !== "string") {
		throw new Error("Invalid timestamp: must be a string");
	}

	return payload as CCPatientWebhookPayload;
}
