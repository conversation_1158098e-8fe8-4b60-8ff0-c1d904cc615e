import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { requestId } from "hono/request-id";
import { handleErrorCleanup } from "@/handlers/adminHandler";
import { handleCCWebhook } from "@/handlers/ccHandler";

const app = new Hono<Env>();
app.use(contextStorage());
app.use("*", requestId());

app.onError((err, c) => {
	console.error(err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId: c.get("requestId"),
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));

// CC Webhook endpoint
app.post("/webhooks/cc", handleCCWebhook);

// Admin error cleanup endpoint
app.get("/admin/cleanup-errors", handleErrorCleanup);

export default app;
